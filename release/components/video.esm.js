/* Dxplus v1.2.2-beta3 */
module.exports=function(t){var n={};function e(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:r})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var o in t)e.d(r,o,function(n){return t[n]}.bind(null,o));return r},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s="AzZk")}({"/b8u":function(t,n,e){var r=e("STAE");t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},"0Dky":function(t,n){t.exports=function(t){try{return!!t()}catch(t){return!0}}},"0GbY":function(t,n,e){var r=e("2oRo"),o=function(t){return"function"==typeof t?t:void 0};t.exports=function(t,n){return arguments.length<2?o(r[t]):r[t]&&r[t][n]}},"1/HG":function(t,n,e){"use strict";e.d(n,"b",(function(){return r})),e.d(n,"a",(function(){return o}));e("sMBO");var r=function(t){"undefined"!=typeof window&&window.Vue&&window.Vue.use(t)},o=function(t,n){t.component(n.name,n)}},"2bX/":function(t,n,e){var r=e("0GbY"),o=e("/b8u");t.exports=o?function(t){return"symbol"==typeof t}:function(t){var n=r("Symbol");return"function"==typeof n&&Object(t)instanceof n}},"2oRo":function(t,n,e){(function(n){var e=function(t){return t&&t.Math==Math&&t};t.exports=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof n&&n)||function(){return this}()||Function("return this")()}).call(this,e("yLpj"))},"9rR5":function(t,n,e){"use strict";e("LVXD")},AzZk:function(t,n,e){"use strict";e.r(n),e.d(n,"MomVideo",(function(){return i}));var r={name:"MomVideo",release:"1.1.0",lastUpdated:"1.1.0",props:{embedUrl:{type:String,required:!0}}},o=(e("9rR5"),e("KHd+")),i=Object(o.a)(r,(function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"MomVideo"},[e("iframe",{attrs:{title:"MomVideo",src:t.embedUrl,"frame-border":"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"}})])}),[],!1,null,"8ff57096",null).exports,u=e("1/HG"),c={install:function(t){Object(u.a)(t,i)}};Object(u.b)(c);n.default=c},DPsx:function(t,n,e){var r=e("g6v/"),o=e("0Dky"),i=e("zBJ4");t.exports=!r&&!o((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},HYAF:function(t,n){t.exports=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t}},"KHd+":function(t,n,e){"use strict";function r(t,n,e,r,o,i,u,c){var f,a="function"==typeof t?t.options:t;if(n&&(a.render=n,a.staticRenderFns=e,a._compiled=!0),r&&(a.functional=!0),i&&(a._scopeId="data-v-"+i),u?(f=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),o&&o.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(u)},a._ssrRegister=f):o&&(f=c?function(){o.call(this,(a.functional?this.parent:this).$root.$options.shadowRoot)}:o),f)if(a.functional){a._injectStyles=f;var s=a.render;a.render=function(t,n){return f.call(n),s(t,n)}}else{var l=a.beforeCreate;a.beforeCreate=l?[].concat(l,f):[f]}return{exports:t,options:a}}e.d(n,"a",(function(){return r}))},LQDL:function(t,n,e){var r,o,i=e("2oRo"),u=e("NC/Y"),c=i.process,f=i.Deno,a=c&&c.versions||f&&f.version,s=a&&a.v8;s?o=(r=s.split("."))[0]<4?1:r[0]+r[1]:u&&(!(r=u.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=u.match(/Chrome\/(\d+)/))&&(o=r[1]),t.exports=o&&+o},LVXD:function(t,n,e){},"NC/Y":function(t,n,e){var r=e("0GbY");t.exports=r("navigator","userAgent")||""},SFrS:function(t,n,e){var r=e("hh1v");t.exports=function(t,n){var e,o;if("string"===n&&"function"==typeof(e=t.toString)&&!r(o=e.call(t)))return o;if("function"==typeof(e=t.valueOf)&&!r(o=e.call(t)))return o;if("string"!==n&&"function"==typeof(e=t.toString)&&!r(o=e.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},STAE:function(t,n,e){var r=e("LQDL"),o=e("0Dky");t.exports=!!Object.getOwnPropertySymbols&&!o((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},UTVS:function(t,n,e){var r=e("ewvW"),o={}.hasOwnProperty;t.exports=Object.hasOwn||function(t,n){return o.call(r(t),n)}},VpIT:function(t,n,e){var r=e("xDBR"),o=e("xs3f");(t.exports=function(t,n){return o[t]||(o[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.16.2",mode:r?"pure":"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})},ewvW:function(t,n,e){var r=e("HYAF");t.exports=function(t){return Object(r(t))}},"g6v/":function(t,n,e){var r=e("0Dky");t.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},glrk:function(t,n,e){var r=e("hh1v");t.exports=function(t){if(!r(t))throw TypeError(String(t)+" is not an object");return t}},hh1v:function(t,n){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},kOOl:function(t,n){var e=0,r=Math.random();t.exports=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++e+r).toString(36)}},"m/L8":function(t,n,e){var r=e("g6v/"),o=e("DPsx"),i=e("glrk"),u=e("oEtG"),c=Object.defineProperty;n.f=r?c:function(t,n,e){if(i(t),n=u(n),i(e),o)try{return c(t,n,e)}catch(t){}if("get"in e||"set"in e)throw TypeError("Accessors not supported");return"value"in e&&(t[n]=e.value),t}},oEtG:function(t,n,e){var r=e("wE6v"),o=e("2bX/");t.exports=function(t){var n=r(t,"string");return o(n)?n:String(n)}},sMBO:function(t,n,e){var r=e("g6v/"),o=e("m/L8").f,i=Function.prototype,u=i.toString,c=/^\s*function ([^ (]*)/,f="name";r&&!(f in i)&&o(i,f,{configurable:!0,get:function(){try{return u.call(this).match(c)[1]}catch(t){return""}}})},tiKp:function(t,n,e){var r=e("2oRo"),o=e("VpIT"),i=e("UTVS"),u=e("kOOl"),c=e("STAE"),f=e("/b8u"),a=o("wks"),s=r.Symbol,l=f?s:s&&s.withoutSetter||u;t.exports=function(t){return i(a,t)&&(c||"string"==typeof a[t])||(c&&i(s,t)?a[t]=s[t]:a[t]=l("Symbol."+t)),a[t]}},wE6v:function(t,n,e){var r=e("hh1v"),o=e("2bX/"),i=e("SFrS"),u=e("tiKp")("toPrimitive");t.exports=function(t,n){if(!r(t)||o(t))return t;var e,c=t[u];if(void 0!==c){if(void 0===n&&(n="default"),e=c.call(t,n),!r(e)||o(e))return e;throw TypeError("Can't convert object to primitive value")}return void 0===n&&(n="number"),i(t,n)}},xDBR:function(t,n){t.exports=!1},xs3f:function(t,n,e){var r=e("2oRo"),o=e("zk60"),i="__core-js_shared__",u=r[i]||o(i,{});t.exports=u},yLpj:function(t,n){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},zBJ4:function(t,n,e){var r=e("2oRo"),o=e("hh1v"),i=r.document,u=o(i)&&o(i.createElement);t.exports=function(t){return u?i.createElement(t):{}}},zk60:function(t,n,e){var r=e("2oRo");t.exports=function(t,n){try{Object.defineProperty(r,t,{value:n,configurable:!0,writable:!0})}catch(e){r[t]=n}return n}}});