const icons = {
  home: require("./icon-home.svg"),
  "log-out": require("./icon-logout.svg"),
  profile: require("./icon-profile.svg"),
  close: require("./icon-close.svg"),
  "arrow-up": require("./icon-arrow-up.svg"),
  "arrow-down": require("./icon-arrow-down.svg"),
  "arrow-left": require("./icon-arrow-left.svg"),
  "arrow-right": require("./icon-arrow-right.svg"),
  "chevron-up": require("./icon-chevron-up.svg"),
  "chevron-down": require("./icon-chevron-down.svg"),
  "chevron-left": require("./icon-chevron-left.svg"),
  "chevron-right": require("./icon-chevron-right.svg"),
  "chevron-first": require("./icon-chevron-first.svg"),
  "chevron-last": require("./icon-chevron-last.svg"),
  search: require("./icon-search.svg"),
  edit: require("./icon-edit.svg"),
  print: require("./icon-print.svg"),
  save: require("./icon-save.svg"),
  delete: require("./icon-delete.svg"),
  "open-in-new": require("./icon-open-in-new.svg"),
  calendar: require("./icon-calendar.svg"),
  time: require("./icon-time.svg"),
  add: require("./icon-add.svg"),
  remove: require("./icon-remove.svg"),
  terminate: require("./icon-terminate.svg"),
  sort: require("./icon-sort.svg"),
  "sort-up": require("./icon-sort-up.svg"),
  "sort-down": require("./icon-sort-down.svg"),
  download: require("./icon-download.svg"),
  upload: require("./icon-upload.svg"),
  "document-upload": require("./icon-document-upload.svg"),
  checkbox: require("./icon-checkbox.svg"),
  "checkbox-checked": require("./icon-checkbox-checked.svg"),
  "radio-button": require("./icon-radio-button.svg"),
  "radio-button-checked": require("./icon-radio-button-checked.svg"),
  error: require("./icon-error.svg"),
  warning: require("./icon-warning.svg"),
  info: require("./icon-info.svg"),
  success: require("./icon-success.svg"),
  question: require("./icon-question.svg"),
  incomplete: require("./icon-incomplete.svg"),
  lightbulb: require("./icon-lightbulb.svg"),
  "internet-lost": require("./icon-internet-lost.svg"),
  dot: require("./icon-dot.svg"),
  menu: require("./icon-menu.svg"),
  // info-service
  list: require("./icon-list.svg"),
  location: require("./icon-location.svg"),
  telephone: require("./icon-telephone.svg"),
  laptop: require("./icon-laptop.svg"),
  form: require("./icon-form.svg"),
  news: require("./icon-news.svg"),
  graph: require("./icon-graph.svg"),
  safety: require("./icon-safety.svg"),
  link: require("./icon-link.svg"),
  "profile-2": require("./icon-profile-2.svg"),
  "lightbulb-2": require("./icon-bulb.svg"),
  message: require("./icon-message.svg"),
  printer: require("./icon-printer.svg"),
  calculator: require("./icon-calculator.svg"),
  dots: require("./icon-dots.svg"),
  book: require("./icon-book.svg"),
  mic: require("./icon-mic.svg"),
  page: require("./icon-page.svg"),
  setting: require("./icon-setting.svg"),
  email: require("./icon-email.svg"),
  "arrow-circle-right": require("./icon-arrow-circle-right.svg"),
  "loading-spinner": require("./icon-loading-spinner.svg"),
  "power": require("./icon-power.svg"),
  "card-payment": require("./icon-card-payment.svg"),
  "filter": require("./icon-filter.svg"),
  "reset": require("./icon-reset.svg"),
  "plus": require("./icon-plus.svg"),
  "minus": require("./icon-minus.svg"),
  "tick": require("./icon-tick.svg"),
  "tick-circle": require("./icon-tick-circle.svg"),
  "identity": require("./icon-identity.svg"),
  "log-in": require("./icon-login.svg"),
  "file": require("./icon-file.svg"),
  "files-upload": require("./icon-files-upload.svg"),
  "hide": require("./icon-hide.svg"),
  "rotate": require("./icon-rotate.svg"),
  "crop": require("./icon-crop.svg"),
  "zoom-out": require("./icon-zoom-out.svg"),
  "zoom-in": require("./icon-zoom-in.svg"),
  "file-pdf": require("./icon-file-pdf.svg"),
  "file-word": require("./icon-file-word.svg"),
  "file-ppt": require("./icon-file-ppt.svg"),
  "file-jpeg": require("./icon-file-jpeg.svg"),
  "file-png": require("./icon-file-png.svg"),
  "file-excel": require("./icon-file-excel.svg"),
  govt: require("./icon-govt.svg"),
  lock: require("./icon-lock.svg"),
  "shopping-cart": require("./icon-shopping-cart.svg"),
  notification: require("./icon-notification.svg")
}

const sortedIcons = Object.keys(icons).sort().reduce(
  (obj, key) => {
    obj[key] = icons[key]
    return obj
  },
  {}
)

export default sortedIcons;