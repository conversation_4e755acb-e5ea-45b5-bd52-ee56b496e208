const webpack = require('webpack')

const MiniCssExtractPlugin = require("mini-css-extract-plugin")
const { VueLoaderPlugin } = require("vue-loader")

const utils = require('./utils')

exports.webpackBaseConfig = {
    mode: process.env.NODE_ENV === "production" ? "production" : "development",
    resolve: {
        extensions: [".js", ".vue", ".json"],
        alias: {
            vue$: "vue/dist/vue.esm.js",
            "@": utils.resolvePath("src"),
        },
    }, 
    module: {
        rules: [
            {
                test: /\.vue$/,
                loader: "vue-loader",
                options: {
                    cacheBusting: true, // config.dev.cacheBusting,
                    transformAssetUrls: {
                        video: ["src", "poster"],
                        source: "src",
                        img: "src",
                        image: "xlink:href",
                    },
                },
            },
            {
                test: /\.css$/,
                use: [
                    process.env.NODE_ENV !== 'production'
                        ? 'vue-style-loader'
                        : MiniCssExtractPlugin.loader,
                    'css-loader'
                ]
            },
            {
                test: /\.scss$/,
                use: [
                    {
                        loader: process.env.NODE_ENV !== 'production'
                            ? 'vue-style-loader'
                            : MiniCssExtractPlugin.loader,
                    },
                    {
                        loader: 'css-loader'
                    },
                    {
                        loader: 'sass-loader',
                        options: {
                            additionalData: `@import "~@/scss/component_import.scss";`
                        }
                    }
                ]
            },
            {
                test: /\.js$/,
                loader: "babel-loader",
                include: [
                    utils.resolvePath("docs"),
                    utils.resolvePath("src"),
                    utils.resolvePath("test"),
                    utils.resolvePath("node_modules/webpack-dev-server/client"),
                ],
            },
        ],
    },
    plugins: [
        new VueLoaderPlugin(),
        // extract css into its own file
        new MiniCssExtractPlugin({
            filename: "[name].css"
        }),
        new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
    ],
    node: {
        // prevent webpack from injecting useless setImmediate polyfill because Vue
        // source contains it (although only uses it if it's native).
        setImmediate: false,
        // prevent webpack from injecting mocks to Node native modules
        // that does not make sense for the client
        dgram: "empty",
        fs: "empty",
        net: "empty",
        tls: "empty",
        child_process: "empty",
    },
}

exports.getAssetsPluginSetting = function(relativeAssetPath) {
    return {
        module: {
            rules: [
                {
                    test: /\.(png|jpe?g|gif)(\?.*)?$/,
                    loader: "url-loader",
                    options: {
                        limit: 10000,
                        // name: utils.assetsPath("img/[name].[hash:7].[ext]"),
                        name: utils.assetsPath("img/[name].[ext]", relativeAssetPath),
                    },
                },
                {
                    test: /\.svg$/,
                    loader: "html-loader",
                },
                {
                    test: /\.html$/,
                    loader: "html-loader",
                },
                {
                    test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                    loader: "url-loader",
                    options: {
                    limit: 10000,
                        // name: utils.assetsPath("media/[name].[hash:7].[ext]"),
                        name: utils.assetsPath("media/[name].[ext]", relativeAssetPath),
                    },
                },
                {
                    test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                    loader: "url-loader",
                    options: {
                    limit: 10000,
                        // name: utils.assetsPath("fonts/[name].[hash:7].[ext]"),
                        name: utils.assetsPath("fonts/[name].[ext]", relativeAssetPath),
                    },
                },
            ]
        }
    }
}