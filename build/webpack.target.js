/**
 * This defines 3 output targets of this library: esm, umd and component esm
 * The structure is to utilise Webpack MultiCompiler (https://webpack.js.org/api/node/#multicompiler)
 * and webpack-merge (https://www.npmjs.com/package/webpack-merge)
 * to build all targets at once, or each separately
 */

/**
 * Some known issues:
 * (1) assets are rebuilt each time a target is built
 * (2) css content is inside js file, making file size big
 * (3) assets filename cannot be hashed, or else they cannot be used across building targets
 */

const fs = require('fs')
const { mergeWithCustomize, customizeArray, unique } = require('webpack-merge')

const utils = require('./utils')
const ignoredComponents = require('../ignoredComponents.json')

const componentDir = 'src/components/'

const componentFolders = fs
    .readdirSync(utils.resolvePath(componentDir))
    .filter(function(f) {
        return fs.statSync(utils.resolvePath(componentDir + f)).isDirectory()
    })
    .filter((c) => !ignoredComponents.includes(c))

const mainEntry = {
    'mom-dxplus': utils.resolvePath('src/index.js'),
}

const componentEntry = componentFolders.reduce((obj, dirName) => {
    obj[dirName] = utils.resolvePath(componentDir + dirName)
    return obj
}, {})

module.exports = {
    esm: {
        config: {
            entry: mainEntry,
            output: {
                filename: '[name].esm.js',
                libraryTarget: 'commonjs2',
                path: utils.resolveVersionPath('build/esm'),
            },
        },
        relativeAssetPath: '../assets',
    },
    umd: {
        config: {
            entry: mainEntry,
            output: {
                filename: '[name].umd.js',
                libraryTarget: 'umd',
                //https://webpack.js.org/configuration/output/#outputlibrary
                library: 'MomDxplus',
                // https://webpack.js.org/configuration/output/#outputumdnameddefine
                umdNamedDefine: true,
                // https://webpack.js.org/configuration/output/#outputglobalobject
                globalObject: 'this',
                path: utils.resolveVersionPath('build/umd'),
            }
        },
        relativeAssetPath: '../assets',
    },
    esmComponents: {
        config: {
            entry: componentEntry,
            output: {
                filename: '[name].esm.js',
                libraryTarget: 'commonjs2',
                path: utils.resolveVersionPath('build/esm/components'),
            },
        },
        relativeAssetPath: '../../assets',
    },
    esmTemplates: {
        config: {
            entry: utils.resolvePath('src/templates/index.js'),
            output: {
                filename: 'template.esm.js',
                libraryTarget: 'commonjs2',
                path: utils.resolveVersionPath('build/esm'),
            },
        },
        relativeAssetPath: '../assets',
    },
    'esm-release': {
        config: {
            entry: mainEntry,
            output: {
                filename: '[name].esm.js',
                libraryTarget: 'commonjs2',
                path: utils.resolvePath('release'),
            },
        },
        relativeAssetPath: './assets',
    },
    'esmComponent-release': {
        config: {
            entry: componentEntry,
            output: {
                filename: '[name].esm.js',
                libraryTarget: 'commonjs2',
                path: utils.resolvePath('release/components'),
            },
        },
        relativeAssetPath: '../assets',
    },
    'esmTemplate-release': {
        config: {
            entry: utils.resolvePath('src/templates/index.js'),
            output: {
                filename: 'template.esm.js',
                libraryTarget: 'commonjs2',
                path: utils.resolvePath('release'),
            },
        },
        relativeAssetPath: '../assets',
    },
}