const path = require('path')
const CopyPlugin  = require("copy-webpack-plugin")

const _package = require('../package.json')

/**
 * Return relative path for dist asset directory 
 * @param {*} _path 
 * @param {*} _assetSubDirectory
 */
exports.assetsPath = function(_path, assetsSubDirectory) {
    return path.posix.join(assetsSubDirectory, _path)
}

/**
 * Resolve dirname and return absolute path
 * Mainly use to handle path in src
 * @param {*} dir 
 */
exports.resolvePath = function(dir) {
    return path.join(__dirname, "..", dir)
}

/**
 * Copy assets from src to dist
 * Apply to one target only to avoid duplication
 */
exports.copyAssetsPlugin = function(from, to) {
    return new CopyPlugin({
        patterns: [
            {
                from: from,
                to: to,
                globOptions: {
                    dot: true,
                    ignore: [
                        "**/data/*",
                        "**/webfonts/**/*",
                        "**/docs/*"
                    ],
                }
            },
        ]
    })
}

/**
 * Return build folder with current version
 * @param {*} subFolder
 */
exports.resolveVersionPath = function(subFolder) {
    return path.join(__dirname, "..", "dist", `${_package.version}/${subFolder}`)
}