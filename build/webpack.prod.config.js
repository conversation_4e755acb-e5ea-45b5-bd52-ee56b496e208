const webpack = require('webpack')
const OptimizeCSSPlugin = require("optimize-css-assets-webpack-plugin")
const TerserPlugin = require('terser-webpack-plugin')
const SafeParser = require("postcss-safe-parser")
const CompressionWebpackPlugin = require('compression-webpack-plugin')

module.exports = {
    plugins: [
        // http://vuejs.github.io/vue-loader/en/workflow/production.html
        new webpack.DefinePlugin({
            "process.env": {
                NODE_ENV: '"production"'
            },
        }),
        // Compress extracted CSS. We are using this plugin so that possible
        // duplicated CSS from different components can be deduped.
        new OptimizeCSSPlugin({
            cssProcessorOptions: { parser: SafeParser },
        }),
        // keep module.id stable when vendor modules does not change
        new webpack.HashedModuleIdsPlugin(),
        // new CompressionWebpackPlugin({
        //     filename: '[name].gz[query]',
        //     algorithm: 'gzip',
        //     test: /\.(js|css|html|svg)$/,
        //     threshold: 10240,
        //     minRatio: 0.8
        // })
        // enable scope hoisting
        // new webpack.optimize.ModuleConcatenationPlugin(),
        
    ],
    optimization: {
        concatenateModules: true,
        // splitChunks: {
        //     chunks: "all",
        //     cacheGroups: {
        //     vendor: {
        //         name: "vendor",
        //         test: /[\\/]node_modules[\\/]/,
        //         enforce: true,
        //     },
        //     },
        // },
        // runtimeChunk: "single",
        minimize: true,
        minimizer: [
            new TerserPlugin({
                extractComments: false,
                terserOptions: {
                    compress: {
                        warnings: false,
                    },
                },
                sourceMap: false,
                parallel: true,
            }),
        ],
    },
}