const path = require("path");

const _package = require("../package.json");

const ignoredComponents = require("../ignoredComponents.json");
const ignorePaths = ignoredComponents.map(
  (c) => "**/components/**/" + c + ".vue"
);
module.exports = {
  version: _package.version,
  // set your styleguidist configuration here
  title: "MOM DXPlus Design System",
  showSidebar: true,
  copyCodeButton: true,
  jssThemedEditor: false,
  /**
   * Ignore app.vue, tests.
   * and other components listed in ignoredComponents.json
   */
  ignore: [
    "**/App.vue",
    "**/_*.vue",
    "**/__tests__/**",
    "**/*.test.js",
    "**/*.test.jsx",
    "**/*.spec.js",
    "**/*.spec.jsx",
    ...ignorePaths,
  ],
  skipComponentsWithoutExample: false,
  exampleMode: "collapse", // collapse code content by default
  tocMode: "collapse", // collapse side menu by default
  usageMode: "expand", // expand info of props/methods by default
  /**
   * Path to static assets directory
   */
  assetsDir: path.join(__dirname, "../src/assets"),
  pagePerSection: true,
  /**
   * Theme of document
   */
  theme: {
    fontFamily: {
      base: ["Open Sans", "Lato", "sans-serif"],
    },
    sidebarWidth: 240,
    Logo: {
      logo: {
        fontSize: 20,
      },
    },
  },
  template: {
    title: "Dx+ Portal",
    lang: "en",
    head: {
      meta: [
        {
          name: "description",
          content: "Dx+ Portal",
        },
      ],
      scripts: [
        {
          src: "https://assets.dcube.cloud/scripts/wogaa.js",
        },
      ],
    },
  },
  require: [
    path.join(__dirname, "../styleguide/docs.styles.scss"),
    path.join(__dirname, "../styleguide/docs.helpers.js"),
    path.join(__dirname, "../styleguide/prism.css"),
  ],
  renderRootJsx: path.join(
    __dirname,
    "../styleguide/components/customized/styleguide.root.js"
  ),
  styleguideComponents: {
    ReactComponentRenderer: path.join(
      __dirname,
      "../styleguide/components/customized/ReactComponentRenderer.js"
    ),
    TableOfContents: path.join(
      __dirname,
      "../styleguide/components/customized/TableOfContents.js"
    ),
    SectionRenderer: path.join(
      __dirname,
      "../styleguide/components/customized/SectionRenderer.js"
    ),
    ComponentsListRenderer: path.join(
      __dirname,
      "../styleguide/components/customized/ComponentsListRenderer.js"
    ),
  },
  compilerConfig: {
    target: { ie: 11 },
  },
  /**
   * Hide pathline
   */
  getComponentPathLine(componentPath) {
    return null;
  },
  components: "src/components/**/*.vue",
  // template: {
  // 	head: {
  // 		meta: [
  // 			{
  // 				name: 'Content-Security-Policy',
  // 				content: "default-src 'self' *.gov.sg https://*.dcube.cloud/ https://*.demdex.net/ https://cm.everesttech.net/ https://wogadobeanalytics.sc.omtrdc.net/; script-src 'self' *.gov.sg assets.adobedtm.com *.addthis.com www.google-analytics.com www.google.com www.googletagmanager.com www.gstatic.com https://*.dcube.cloud https://assets.adobedtm.com/ 'unsafe-eval' 'unsafe-inline' http: https:; object-src 'none'; style-src 'self' *.gov.sg fonts.googleapis.com *.dcube.cloud www.google.com https://assets.dcube.cloud/fonts/ 'unsafe-inline'; img-src * data: https://wogadobeanalytics.sc.omtrdc.net/ https://cm.everesttech.net/ https://dpm.demdex.net/; media-src *; font-src * data: https://assets.dcube.cloud/fonts/; child-src 'self' *.gov.sg *.google.com dpm.demdex.net *.youtube.com *.youtube-nocookie.com *.vimeo.com *.addthis.com fast.dpm.demdex.net; frame-src 'self' *.gov.sg *.google.com dpm.demdex.net *.youtube.com *.youtube-nocookie.com *.vimeo.com *.addthis.com fast.dpm.demdex.net; connect-src 'self' *.gov.sg dpm.demdex.net www.google-analytics.com stats.g.doubleclick.net *.dcube.cloud fonts.googleapis.com wogadobeanalytics.sc.omtrdc.net *.addthis.com https://*.dcube.cloud"
  // 			},
  // 			{
  // 				name: 'X-Frame-Options',
  // 				content: 'SAMEORIGIN'
  // 			}
  // 		]
  // 	},
  // }
};
