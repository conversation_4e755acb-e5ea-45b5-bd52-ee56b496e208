const path = require('path')

const _package = require('../package.json')
const baseDocsConfig = require('./doc.base.config')

const componentsDocsSections = require('../styleguide/sections/components')
const templatesDocsSections = require('../styleguide/sections/templates')

const ports = {
  "components": "6061",
  "templates": "6062",
}

const baseComponentsConfig = {
  ...baseDocsConfig,
  "title": "MOM | Components",
  "serverPort": ports.components,
  "sections": componentsDocsSections,
  styleguideComponents: {
    ...baseDocsConfig.styleguideComponents,
    StyleGuideRenderer: path.join(__dirname, '../styleguide/components/customized/DocsComponentRoot.js'),
  },
}

const baseTemplatesConfig = {
  ...baseDocsConfig,
  "title": "MOM | Templates",
  "showSidebar": false,
  "sections": templatesDocsSections,
  "serverPort": ports.templates,
  "jssThemedEditor": false,
  "require": [
    path.join(__dirname, '../styleguide/docs.styles.scss'),
    path.join(__dirname, '../styleguide/docs.template.helpers.js'),
    path.join(__dirname, '../styleguide/docs.helpers.js'),
    path.join(__dirname, "../styleguide/prism.css"),
  ],
  styleguideComponents: {
    ...baseDocsConfig.styleguideComponents,
    StyleGuideRenderer: path.join(__dirname, '../styleguide/components/customized/DocsTemplateRoot.js'),
  },
}

module.exports = ({
  "lib": {
    "assetUrl": "https://psesm.tsp.sg/assets/"
  },
  "docs": {
    "latest": {
      "rimrafDir": `dist/latest/styleguide`,
      "components": {
        ...baseComponentsConfig,
        "styleguideDir": `dist/latest/styleguide/components`,
      },
      "templates": {
        ...baseTemplatesConfig,
        "styleguideDir": `dist/latest/styleguide/templates`,
      }
    },
    "version": {
      "rimrafDir": `dist/${_package.version}/styleguide`,
      "styleguidePublicPath": `/v/${_package.version}/`,
      "components": {
        ...baseComponentsConfig,
        "styleguideDir": `dist/${_package.version}/styleguide/components`,
        "styleguidePublicPath": `/v${_package.version}/`,
      },
      "templates": {
        ...baseTemplatesConfig,
        "styleguideDir": `dist/${_package.version}/styleguide/templates`,
        "styleguidePublicPath": `/v/${_package.version}/`,
      }
    }
  }
});