const ncp = require('ncp').ncp
const chalk = require("chalk")
const utils = require('./utils')

const source = utils.resolvePath("dist/latest/styleguide")
const destination = utils.resolvePath("../dxportal/dist")

ncp.limit = 16
ncp(source, destination, function (err) {
    if (err) {
        console.log("hey")
        return console.log(chalk.bold.red(err))
    }
    console.log(chalk.green(`Done copying to ${destination}`))
})