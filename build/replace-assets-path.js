const fs = require('fs')
const glob = require('glob')
const chalk = require('chalk')

const _package = require('../package.json')
const utils = require('./utils')
const config = require('./config')

const dir = utils.resolvePath(`dist/${_package.version}`)

try {
  console.log(chalk.green('Modifying asset paths of css files in lib...'))
  glob(`${dir}/**/*.css`, function(err, res) {
    if (err) throw err
    if (res.length === 0)
      console.log(chalk.yellowBright(`No css file found in ${dir}`))
    else {
      res.forEach(function (cssFilePath) {
        const content = fs.readFileSync(cssFilePath, {encoding:'utf8', flag:'r'})
        const newContent = content.toString().split('../assets/').join(config.lib.assetUrl)
        fs.writeFileSync(cssFilePath, newContent)
      })
      console.log(chalk.green(`Completed modifying asset paths for ${res.length} files in ${dir}`))
    }
  })
} catch (e) {
  console.log(chalk.red.bold('Failed to modify asset paths.'))
  console.log(e)
}