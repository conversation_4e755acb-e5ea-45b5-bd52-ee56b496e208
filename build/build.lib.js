process.env.NODE_ENV = "production"

const fs = require('fs')
const path = require("path")
const ora = require("ora")
const chalk = require("chalk")
const rimraf = require('rimraf')
const webpack = require('webpack')
const { merge } = require('webpack-merge')
const glob = require('glob')

const _package = require('../package.json')
const webpackTarget = require('./webpack.target')
const { webpackBaseConfig, getAssetsPluginSetting } = require('./webpack.base.config')
const webpackProdConfig = require('./webpack.prod.config')
const { copyAssetsPlugin, resolvePath } = require('./utils')

const outputPath = path.resolve(__dirname, '../dir', _package.version, 'build')

const spinner = ora("Building Design System system files...")
spinner.start()

rimraf(outputPath, function (rimrafErr) {
    if (rimrafErr) throw rimrafErr
    webpack(
        [
            merge(
                webpackBaseConfig,
                webpackProdConfig,
                webpackTarget.umd.config,
                getAssetsPluginSetting(webpackTarget.umd.relativeAssetPath),
                {
                    plugins: [
                        copyAssetsPlugin(
                            path.resolve(__dirname, "../src/assets"),
                            "../assets",
                        )
                    ]
                },
            ),
            merge(
                webpackBaseConfig,
                webpackProdConfig,
                webpackTarget.esm.config,
                getAssetsPluginSetting(webpackTarget.esm.relativeAssetPath),
            ),
            merge(
                webpackBaseConfig,
                webpackProdConfig,
                webpackTarget.esmComponents.config,
                getAssetsPluginSetting(webpackTarget.esmComponents.relativeAssetPath),
            ),
            merge(
                webpackBaseConfig,
                webpackProdConfig,
                webpackTarget.esmTemplates.config,
                getAssetsPluginSetting(webpackTarget.esmTemplates.relativeAssetPath),
            ),
        ],
        function(err, multiStats) {
            spinner.stop()
            if (err) throw err
            multiStats.stats.forEach(function(stats) {
                process.stdout.write(
                    stats.toString({
                        colors: true,
                        modules: false,
                        children: false,
                        chunks: false,
                        chunkModules: false,
                    }) + "\n\n"
                )
            })
            if (multiStats.hasErrors()) {
                console.log(chalk.red("  Design System system files build failed with errors.\n"))
                process.exit(1)
            }
        
            // Remove unneeded css file
            glob(resolvePath(`dist/${_package.version}/build/esm/components/*.css`), function(err, res) {
                if (err) throw err
                if (res.length > 0)
                  res.forEach(function (cssFilePath) {
                    fs.unlinkSync(cssFilePath);
                  })
            })
            console.log(chalk.green("  Design System system files build complete.\n"))
            // console.log(
            //     chalk.cyan(
            //     "  Tip: You can now publish the design system as a private NPM module.\n" +
            //         "  Users can import the module using:\n"
            //     )
            // )
            // console.log(
            //     chalk.yellow(
            //     "  import MomDesignSystem from 'mom-design-system'\n" +
            //         "  import 'mom-design-system/dist/system/css/global.min.css'\n" +
            //         "  import 'mom-design-system/dist/system/css/carapace.min.css'\n" +
            //         "  import 'mom-design-system/dist/system/system.css'\n\n" +
            //         "  Vue.use(MomDesignSystem)\n"
            //     )
            // )
        }
    )
})