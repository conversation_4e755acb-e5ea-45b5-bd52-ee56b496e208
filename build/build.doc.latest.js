const path = require("path")
const styleguidist = require('vue-styleguidist')
const rimraf = require('rimraf')
const ora = require("ora")
const chalk = require("chalk")

const config = require('./config')

const spinner = ora("Building version documentation...")
spinner.start()

rimraf(path.resolve(__dirname, '..', config.docs.latest.rimrafDir), function (rimrafErr) {
  if (rimrafErr) throw rimrafErr
  styleguidist.default(config.docs.latest.components)
    .build((err, cfg) => {
      if (err) {
        spinner.stop()
        throw err
      }

      styleguidist.default(config.docs.latest.templates)
        .build((err2, cfg2) => {
          spinner.stop()
          if (err2) throw err2
          console.log(chalk.green(` Version documenation published to: ${config.docs.latest.rimrafDir}`))
        })
    }
  )
})