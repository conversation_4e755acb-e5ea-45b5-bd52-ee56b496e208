/**
 * Replace version in url path in public folder and dist after production build
 * Replace slack and wogaa urls for production
 * Replace font definitions in built files
 * This is to ensure url version in public and dist folder is aligned with package.json version
 */

/**
 * Configuration
 */
var slackUat = "dxplus-uat.slack.com";
var slackProd = "dxplus.slack.com";

var wogaaUat = "https://assets.dcube.cloud/scripts/wogaa.js";
var wogaaProd = "https://assets.wogaa.sg/scripts/wogaa.js";

var versionMatchString = "{VERSION_NO}";
var dateMatchString = "{CURRENT_DATE}";
var yearMatchString = "{CURRENT_YEAR}";

// Font replacement configuration
const fontToReplace = /font-family:BlinkMacSystemFont,-apple-system,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,Helvetica,Arial,sans-serif/g;
const replacementFont = 'font-family:inherit';

const umdFileName = "mom-dxplus.umd.js";
const jsFileName = "mom-dxplus.js";

/**
 * Include libraries
 */
var fs = require("fs");
const glob = require("glob");
const chalk = require("chalk");
const dayjs = require("dayjs");
var pjson = require("../package.json");
const path = require("path");

//Destination Directory
const destDir = "./dist";
const releaseDir = "./release";

//Current Version
var current_version = pjson.version.split(".").join("_");
//If production or staging
var isProduction = pjson.prod_env;

var getTodaysDate = (process.env.BUILD_DATE ? dayjs(process.env.BUILD_DATE) : dayjs()).format("DD MMMM YYYY");
var getTodaysYear = (process.env.BUILD_DATE ? dayjs(process.env.BUILD_DATE) : dayjs()).format("YYYY");

try {
  if (isProduction) {
    console.log(
      chalk.green("*******Modifying url paths for PRODUCTION build*******")
    );

    //replace slack urls
    console.log(chalk.green("Modifying slack url paths in dist..."));
    updateSubstring(`${destDir}/**/*.js`, slackUat, slackProd);
    updateSubstring(`${destDir}/**/*.js.map`, slackUat, slackProd);

    //replace wogaa urls
    console.log(chalk.green("Modifying url paths in dist..."));
    updateSubstring(`${destDir}/**/*.js`, wogaaUat, wogaaProd);
    updateSubstring(`${destDir}/**/*.html`, wogaaUat, wogaaProd);
  }

  //replace version strings
  updateSubstring(`${destDir}/**/*.html`, versionMatchString, current_version);
  updateSubstring(`${destDir}/**/*.js`, versionMatchString, current_version);

  //replace date strings
  updateSubstring(`${destDir}/**/*.html`, dateMatchString, getTodaysDate);
  updateSubstring(`${destDir}/**/*.js`, dateMatchString, getTodaysDate);
  updateSubstring(`${destDir}/**/*.js.map`, dateMatchString, getTodaysDate);
  updateSubstring(`${destDir}/**/*.txt`, dateMatchString, getTodaysDate);

  //replace year strings
  updateSubstring(`${destDir}/**/*.html`, yearMatchString, getTodaysYear);
  updateSubstring(`${destDir}/**/*.js`, yearMatchString, getTodaysYear);
  updateSubstring(`${destDir}/**/*.js.map`, yearMatchString, getTodaysYear);
  updateSubstring(`${destDir}/**/*.txt`, yearMatchString, getTodaysYear);

  //Configure links to work for subversion
  if (current_version.match(/[A-Z]$/i)) {
    const versionUrl = current_version.split("_").join(".")

    console.log(chalk.green("Modifying url paths for /components and /templates for subversion in dist..."));
    const componentsUrl = "/components/index.html";
    const versionComponentsUrl = "/" + versionUrl + componentsUrl;
    updateSubstring(`${destDir}/**/*.html`, componentsUrl, versionComponentsUrl);
    updateSubstring(`${destDir}/**/*.js`, componentsUrl, versionComponentsUrl);

    const templatesUrl = "/templates/index.html";
    const versionTemplatesUrl = "/" + versionUrl + templatesUrl;
    updateSubstring(`${destDir}/**/*.html`, templatesUrl, versionTemplatesUrl);
    updateSubstring(`${destDir}/**/*.js`, templatesUrl, versionTemplatesUrl);

    console.log(chalk.green("Modifying url paths for portal links for subversion in dist..."));
    const portalUrlPrefix = "/#/"
    const versionPortalUrlPrefix = "../index.html#/"
    updateSubstring(`${destDir}/**/*.html`, portalUrlPrefix, versionPortalUrlPrefix);
    updateSubstring(`${destDir}/**/*.js`, portalUrlPrefix, versionPortalUrlPrefix);
  }

  //rename umd js file
  renameAll(`${destDir}/**/*.js`, umdFileName, jsFileName, true);

  //Adding version number to release and umd files and processing fonts
  addVersionCommentAndProcessFonts(`${releaseDir}/**/*.js`, ``);
  addVersionCommentAndProcessFonts(`${releaseDir}/**/*.css`, ``);
  addVersionCommentAndProcessFonts(`${destDir}/**/build/**/*.js`, ``);
  addVersionCommentAndProcessFonts(`${destDir}/**/build/**/*.css`, ``);

} catch (e) {
  console.log(chalk.red.bold("Failed to modify files."));
  console.log(e);
}

function updateSubstring(destDir, substring, replaceWith) {
  let count = 0;
  glob(`${destDir}`, function(err, res) {
    if (err) throw err;
    if (res.length === 0)
      console.log(chalk.yellowBright(`No file found in ${destDir}`));
    else {
      res.forEach(function(txtFilePath) {
        count++;
        const content = fs.readFileSync(txtFilePath, {
          encoding: "utf8",
          flag: "r",
        });
        const newContent = content
          .toString()
          .split(substring)
          .join(replaceWith);
        fs.writeFileSync(txtFilePath, newContent);
      });
      console.log(
        chalk.green(
          `Completed Replacing ${substring} with ${replaceWith} for ${count} files`
        )
      );
    }
  });
}

function renameAll(dir, oldName, newName, sync) {
  let count = 0;
  const callBackFn = (err, res) => {
    if (err) throw err;
    if (res.length === 0)
      console.log(chalk.yellowBright(`No file found in ${destDir}`));
    else {
      res.forEach(function(txtFilePath) {
        var filename = path.basename(txtFilePath);
        if (filename === oldName) {
          var newFilePath = txtFilePath.replace(oldName, newName);
          fs.renameSync(txtFilePath, newFilePath);
          count++;
        }
      });
    }
  };
  if (sync) {
    const res = glob.sync(`${dir}`);
    callBackFn(undefined, res);
  } else {
    glob(`${dir}`, callBackFn);
  }
  
  console.log(
    chalk.green(
      `Completed Renaming ${oldName} with ${newName} for ${count} files`
    )
  );
}

function processFile(filepath, template) {
  let content = fs.readFileSync(filepath, {
    encoding: "utf8",
    flag: "r",
  });

  // Process font-family declarations
  content = content.replace(fontToReplace, replacementFont);
  
  // Add version template
  const newContent = template + content;
  fs.writeFileSync(filepath, newContent);
}

function addVersionCommentAndProcessFonts(dir, fileName) {
  const versionUrl = current_version.split("_").join(".")
  let template = `/* Dxplus v${versionUrl} */\n`;
  let count = 0;
  glob(`${dir}`, (err, res) => {
    if(err) {
      console.log('Error');
      throw err;
    }
    if(res.length === 0) {
      console.log(chalk.yellowBright(`No file found in ${destDir}`));
    } else {
      res.forEach((txtFilePath) => {
        let filename = path.basename(txtFilePath);
        if(fileName && fileName !== '') {
          if(filename === fileName) {
            processFile(txtFilePath, template);
          }
        } else {
          processFile(txtFilePath, template);
        }
        count++;
      });
      console.log(
        chalk.green(
          `Completed Adding Version Number and Processing Fonts in ${count} files` + 
          (fileName ? ` (${fileName})` : '')
        )
      );
    }
  });
}