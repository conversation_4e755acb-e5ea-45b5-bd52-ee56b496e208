process.env.NODE_ENV = "production"

const fs = require('fs')
const path = require("path")
const ora = require("ora")
const chalk = require("chalk")
const rimraf = require('rimraf')
const webpack = require('webpack')
const { merge, mergeWithCustomize } = require('webpack-merge')
const glob = require('glob')

const _package = require('../package.json')
const webpackTarget = require('./webpack.target')
const { webpackBaseConfig, getAssetsPluginSetting } = require('./webpack.base.config')
const webpackProdConfig = require('./webpack.prod.config')
const { copyAssetsPlugin, resolvePath } = require('./utils')

const outputPath = path.resolve(__dirname, '../dir', _package.version, 'build')

const spinner = ora("Building Design System system files...")
spinner.start()

rimraf(outputPath, function (rimrafErr) {
    if (rimrafErr) throw rimrafErr
    webpack(
        [
            merge(
                webpackBaseConfig,
                webpackProdConfig,
                webpackTarget['esmComponent-release'].config,
                getAssetsPluginSetting(webpackTarget['esmComponent-release'].relativeAssetPath),
                {
                  plugins: [
                      copyAssetsPlugin(
                          path.resolve(__dirname, "../src/assets"),
                          "../assets",
                      )
                  ]
                },
            ),
            merge(
                webpackBaseConfig,
                webpackProdConfig,
                webpackTarget['esmTemplate-release'].config,
                getAssetsPluginSetting(webpackTarget['esmTemplate-release'].relativeAssetPath),
            ),
            merge(
                webpackBaseConfig,
                webpackProdConfig,
                webpackTarget['esm-release'].config,
                getAssetsPluginSetting(webpackTarget['esm-release'].relativeAssetPath),
            ),
        ],
        function(err, multiStats) {
            spinner.stop()
            if (err) throw err
            multiStats.stats.forEach(function(stats) {
                process.stdout.write(
                    stats.toString({
                        colors: true,
                        modules: false,
                        children: false,
                        chunks: false,
                        chunkModules: false,
                    }) + "\n\n"
                )
            })
            if (multiStats.hasErrors()) {
                console.log(chalk.red("  Design System system files build failed with errors.\n"))
                process.exit(1)
            }
        
            // Remove unneeded css file
            glob(resolvePath(`release/components/*.css`), function(err, res) {
                if (err) throw err
                if (res.length > 0)
                  res.forEach(function (cssFilePath) {
                    fs.unlinkSync(cssFilePath);
                  })
            })
            glob(resolvePath(`release/main.css`), function(err, res) {
                if (err) throw err
                if (res.length > 0)
                  res.forEach(function (cssFilePath) {
                    fs.unlinkSync(cssFilePath);
                  })
            })
            console.log(chalk.green("  Design System system files build complete.\n"))
        }
    )
})