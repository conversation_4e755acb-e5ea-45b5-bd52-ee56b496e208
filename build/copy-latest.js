const ncp = require('ncp').ncp
const chalk = require("chalk")
const rimraf = require('rimraf')

const _package = require('../package.json')
const _versionInfo = require('../src/assets//versions.json')
const utils = require('./utils')

const currentVersion = _package.version
const latestVersion = _versionInfo.latest

const source = utils.resolvePath(`dist/${currentVersion}`)
const destination = utils.resolvePath(`dist/latest`)

if (latestVersion !== currentVersion) {
  console.log(chalk.yellow(' No action need to update latest lib: current version is not latest one.'))
  console.log(chalk.yellow(' Check "/src/assets//versions.json" if latest version number is incorrect.'))
} else {
  rimraf(destination, function (rimrafErr) {
    if (rimrafErr) throw rimrafErr
    
    ncp.limit = 16;
    
    console.log(chalk.green(`  Update lib latest version..."`))
    ncp(source, destination, function (err) {
      if (err) {
        console.log("hey")
        return console.log(chalk.bold.red(err))
      }
      console.log(chalk.green(`  Done copy from "dist/${currentVersion}" to "dist/latest"`))
    });
  });
}